/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any */
import type { DataProvider } from "react-admin";

// Simple fake data provider for testing purposes
export const dataProvider: DataProvider = {
  getList: async (resource, _params) => {
    // Return fake data for testing
    const data = [
      {
        id: 1,
        name: `Test ${resource} 1`,
        created_at: new Date().toISOString(),
      },
      {
        id: 2,
        name: `Test ${resource} 2`,
        created_at: new Date().toISOString(),
      },
      {
        id: 3,
        name: `Test ${resource} 3`,
        created_at: new Date().toISOString(),
      },
    ] as any[];

    return Promise.resolve({
      data,
      total: data.length,
    });
  },

  getOne: async (resource, params) => {
    return Promise.resolve({
      data: {
        id: params.id,
        name: `Test ${resource} ${params.id}`,
        created_at: new Date().toISOString(),
      } as any,
    });
  },

  getMany: async (resource, params) => {
    const data = params.ids.map((id) => ({
      id,
      name: `Test ${resource} ${id}`,
      created_at: new Date().toISOString(),
    })) as any[];

    return Promise.resolve({ data });
  },

  getManyReference: async (resource, _params) => {
    const data = [
      {
        id: 1,
        name: `Test ${resource} 1`,
        created_at: new Date().toISOString(),
      },
    ] as any[];

    return Promise.resolve({
      data,
      total: data.length,
    });
  },

  create: async (_resource, params) => {
    return Promise.resolve({
      data: {
        id: Math.random(),
        ...params.data,
        created_at: new Date().toISOString(),
      } as any,
    });
  },

  update: async (_resource, params) => {
    return Promise.resolve({
      data: {
        id: params.id,
        ...params.data,
        updated_at: new Date().toISOString(),
      } as any,
    });
  },

  updateMany: async (_resource, params) => {
    return Promise.resolve({
      data: params.ids,
    });
  },

  delete: async (_resource, params) => {
    return Promise.resolve({
      data: { id: params.id } as any,
    });
  },

  deleteMany: async (_resource, params) => {
    return Promise.resolve({
      data: params.ids,
    });
  },
};
