/**
 * Supabase Mock Users Script
 *
 * This script creates basic auth users for local development and testing.
 * It creates three types of users:
 * - 1 admin user with admin role
 * - 1 provider user with provider role
 * - 1 customer user with customer role (automatically assigned by trigger)
 *
 * The script only creates auth users with roles. No profiles, wallets, or
 * additional setup is performed.
 *
 * The script ensures users are not duplicated by checking existing emails.
 *
 * Usage:
 *   pnpm supabase:mock
 *
 * Requirements:
 *   - Local Supabase instance must be running
 *   - Environment variables must be configured (.env file)
 *   - Database migrations must be applied
 */

import { serviceClient } from "../lib/service-client";
import { randomUUID } from "node:crypto";

// Mock user data configuration
const MOCK_USERS = [
  {
    role: "admin",
    email: "<EMAIL>"
  },
  {
    role: "provider",
    email: "<EMAIL>"
  },
  {
    role: "customer",
    email: "<EMAIL>"
  }
] as const;

/**
 * Check if a user with the given email already exists
 */
async function userExists(email: string): Promise<boolean> {
  try {
    const { data } = await serviceClient.auth.admin.listUsers();
    return data.users.some((user) => user.email === email);
  } catch (error) {
    console.error(`❌ Error checking if user exists: ${email}`, error);
    return false;
  }
}

/**
 * Create a single mock user (auth only)
 */
async function createMockUser(userData: (typeof MOCK_USERS)[number]) {
  const { role, email } = userData;

  console.log(`\n📝 Creating ${role} user: ${email}`);

  try {
    // Check if user already exists
    if (await userExists(email)) {
      console.log(`⚠️  User ${email} already exists, skipping...`);
      return;
    }

    // Create user in auth.users
    const userCreation = await serviceClient.auth.admin.createUser({
      id: randomUUID(),
      email,
      password: "password123",
      email_confirm: true
    });

    if (!userCreation.data.user) {
      throw new Error(`Failed to create ${role} user`);
    }

    const userId = userCreation.data.user.id;
    console.log(`✅ Created auth user: ${userId}`);

    // Assign role (skip for customer as it's auto-assigned by trigger)
    if (role !== "customer") {
      await serviceClient.schema("app_access").rpc("assign_role_to_user", {
        v_user_id: userId,
        v_role_name: role
      });
      console.log(`✅ Assigned ${role} role`);
    }

    console.log(`🎉 Successfully created ${role} user: ${email}`);
  } catch (error) {
    console.error(`❌ Error creating ${role} user:`, error);
    throw error;
  }
}

/**
 * Main function to create all mock users
 */
async function main() {
  console.log("🚀 Starting mock data creation for E-Senpai...\n");

  try {
    // Create all mock users
    for (const userData of MOCK_USERS) {
      await createMockUser(userData);
    }

    console.log("\n🎉 Mock user creation completed successfully!");
    console.log("\n📋 Created users:");
    MOCK_USERS.forEach((user) => {
      console.log(`   • ${user.email} (password: password123)`);
    });
    console.log(
      "\n💡 You can now use these accounts for local development and testing."
    );
    console.log(
      "Note: Only auth users were created. Profiles and wallets need to be set up separately."
    );
  } catch (error) {
    console.error("\n❌ Failed to create mock data:", error);
    process.exit(1);
  }
}

// Run the script
main();
