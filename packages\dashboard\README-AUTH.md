# Dashboard Authentication

This document explains the custom authProvider implementation for the React Admin dashboard.

## Overview

The authProvider integrates with Supabase authentication and the E-Senpai role-based access control system. It provides:

- Email/password authentication via Supabase
- User role and capability management
- Session persistence using localStorage
- Integration with the `user_roles_and_capabilities` database view

## Implementation Details

### File Location

- **AuthProvider**: `src/providers/authProvider.ts`
- **DataProvider**: `src/providers/dataProvider.ts` (simple fake provider for testing)

### Authentication Flow

1. **Login**: User provides email and password

   - Authenticates with Supabase using `signInWithPassword`
   - Fetches user roles and capabilities from `app_access.user_roles_and_capabilities` view
   - Stores user data and permissions in localStorage

2. **Session Check**: On app load and route changes

   - Verifies current session with Supabase
   - Refreshes user data if not stored locally
   - Redirects to login if not authenticated

3. **Logout**: Clears session and stored data
   - Signs out from Supabase
   - Removes localStorage data

### Data Structure

#### User Data

```typescript
interface UserData {
  id: string;
  email: string;
  roles: string[];
  capabilities: string[];
}
```

#### Permissions

```typescript
{
  roles: string[];
  capabilities: string[];
}
```

### Storage Keys

- `auth_user`: Complete user data
- `auth_permissions`: User roles and capabilities

## Usage

### Testing the Authentication

1. **Start the dashboard**:

   ```bash
   pnpm --filter dashboard dev
   ```

2. **Access the login page**: The app will redirect to `/login` if not authenticated

3. **Login with test credentials**: Use these pre-created test accounts:

   - **Admin**: `<EMAIL>` / `password123`
   - **Provider**: `<EMAIL>` / `password123`
   - **Customer**: `<EMAIL>` / `password123`

4. **Check permissions**: Use React Admin's permission hooks to access user roles and capabilities

### Permission Checking

```typescript
import { usePermissions } from 'react-admin';

const MyComponent = () => {
  const { permissions, isLoading } = usePermissions();

  if (isLoading) return <div>Loading...</div>;

  const hasAdminRole = permissions?.roles?.includes('admin');
  const canManageUsers = permissions?.capabilities?.includes('manage_users');

  return (
    <div>
      {hasAdminRole && <AdminPanel />}
      {canManageUsers && <UserManagement />}
    </div>
  );
};
```

## Environment Variables

The dashboard uses these environment variables (configured in `vite.config.ts`):

- `SUPABASE_URL`: Supabase project URL
- `SUPABASE_ANON_KEY`: Supabase anonymous key

## Database Integration

The authProvider queries the `app_access.user_roles_and_capabilities` view:

```sql
SELECT
  user_id,
  ARRAY_AGG(DISTINCT r.name) AS roles,
  ARRAY_AGG(DISTINCT c.name) AS capabilities
FROM app_access.user_role ur
JOIN app_access.role r ON ur.role_id = r.id
JOIN app_access.role_capability rc ON r.id = rc.role_id
JOIN app_access.capability c ON rc.capability_id = c.id
GROUP BY user_id;
```

## Error Handling

- **Authentication errors**: Redirects to login page
- **Network errors**: Logged to console, graceful fallback
- **Missing permissions**: Returns empty arrays, doesn't block functionality
- **Invalid stored data**: Clears localStorage and re-fetches

## Security Considerations

- Uses Supabase's built-in session management
- Stores minimal data in localStorage (no sensitive tokens)
- Validates session on every route change
- Clears data on authentication errors

## Future Enhancements

- Add refresh token handling
- Implement role-based route protection
- Add audit logging for authentication events
- Support for multi-factor authentication
- Integration with React Admin's `canAccess` method for fine-grained permissions
