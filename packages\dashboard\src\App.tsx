import { Admin, Resource, ListGuesser } from "react-admin";
import { Layout } from "./Layout";
import { lightTheme, darkTheme } from "./theme";
import { authProvider } from "./providers/authProvider";
import { dataProvider } from "./providers/dataProvider";

export const App = () => (
  <Admin
    layout={Layout}
    theme={lightTheme}
    darkTheme={darkTheme}
    authProvider={authProvider}
    dataProvider={dataProvider}
  >
    <Resource name="test" list={ListGuesser} />
  </Admin>
);
