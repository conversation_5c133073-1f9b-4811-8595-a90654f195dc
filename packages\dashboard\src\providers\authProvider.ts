import type { AuthProvider } from "react-admin";
import { createClient } from "lib/supabase/client";

interface UserData {
  id: string;
  email: string;
  roles: string[];
  capabilities: string[];
}

// Storage keys for authentication state
const AUTH_STORAGE_KEY = "auth_user";
const PERMISSIONS_STORAGE_KEY = "auth_permissions";

export const authProvider: AuthProvider = {
  // Login with email and password
  async login({ email, password }: { email: string; password: string }) {
    const supabase = createClient();

    try {
      // Sign in with email and password
      const { data: authData, error: authError } =
        await supabase.auth.signInWithPassword({
          email,
          password,
        });

      if (authError) {
        throw new Error(authError.message);
      }

      if (!authData.user) {
        throw new Error("Authentication failed");
      }

      // Fetch user roles and capabilities
      const { data: userRolesData, error: rolesError } = await supabase
        .schema("app_access")
        .from("user_roles_and_capabilities")
        .select("*")
        .eq("user_id", authData.user.id)
        .single();

      if (rolesError && rolesError.code !== "PGRST116") {
        // PGRST116 = no rows returned
        console.warn("Failed to fetch user roles:", rolesError.message);
      }

      // Prepare user data
      const userData: UserData = {
        id: authData.user.id,
        email: authData.user.email || "",
        roles: userRolesData?.roles || [],
        capabilities: userRolesData?.capabilities || [],
      };

      // Store user data and permissions in localStorage
      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(userData));
      localStorage.setItem(
        PERMISSIONS_STORAGE_KEY,
        JSON.stringify({
          roles: userData.roles,
          capabilities: userData.capabilities,
        }),
      );

      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  },

  // Logout user
  async logout() {
    const supabase = createClient();

    try {
      await supabase.auth.signOut();
    } catch (error) {
      console.warn("Error during logout:", error);
    }

    // Clear stored data
    localStorage.removeItem(AUTH_STORAGE_KEY);
    localStorage.removeItem(PERMISSIONS_STORAGE_KEY);

    return Promise.resolve();
  },

  // Check if user is authenticated
  async checkAuth() {
    const supabase = createClient();

    try {
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();

      if (error || !user) {
        // Clear any stale data
        localStorage.removeItem(AUTH_STORAGE_KEY);
        localStorage.removeItem(PERMISSIONS_STORAGE_KEY);
        throw new Error("Not authenticated");
      }

      // Check if we have stored user data
      const storedUserData = localStorage.getItem(AUTH_STORAGE_KEY);
      if (!storedUserData) {
        // If no stored data, fetch it
        const { data: userRolesData, error: rolesError } = await supabase
          .schema("app_access")
          .from("user_roles_and_capabilities")
          .select("*")
          .eq("user_id", user.id)
          .single();

        if (rolesError && rolesError.code !== "PGRST116") {
          console.warn("Failed to fetch user roles:", rolesError.message);
        }

        const userData: UserData = {
          id: user.id,
          email: user.email || "",
          roles: userRolesData?.roles || [],
          capabilities: userRolesData?.capabilities || [],
        };

        localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(userData));
        localStorage.setItem(
          PERMISSIONS_STORAGE_KEY,
          JSON.stringify({
            roles: userData.roles,
            capabilities: userData.capabilities,
          }),
        );
      }

      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  },

  // Check if an error is an authentication error
  async checkError(error: { status?: number; message?: string }) {
    const status = error.status;

    if (status === 401 || status === 403) {
      // Clear stored data on auth errors
      localStorage.removeItem(AUTH_STORAGE_KEY);
      localStorage.removeItem(PERMISSIONS_STORAGE_KEY);
      throw new Error("Session expired");
    }

    // For other errors, don't redirect to login
    return Promise.resolve();
  },

  // Get user identity
  async getIdentity() {
    const storedUserData = localStorage.getItem(AUTH_STORAGE_KEY);

    if (!storedUserData) {
      throw new Error("No user data found");
    }

    try {
      const userData: UserData = JSON.parse(storedUserData);
      return Promise.resolve({
        id: userData.id,
        fullName: userData.email,
        avatar: undefined,
      });
    } catch (error) {
      console.warn("Failed to parse stored user data:", error);
      throw new Error("Invalid user data");
    }
  },

  // Get user permissions (roles and capabilities)
  async getPermissions() {
    const storedPermissions = localStorage.getItem(PERMISSIONS_STORAGE_KEY);

    if (!storedPermissions) {
      return Promise.resolve(undefined);
    }

    try {
      const permissions = JSON.parse(storedPermissions);
      return Promise.resolve(permissions);
    } catch (error) {
      console.warn("Failed to parse stored permissions:", error);
      return Promise.resolve(undefined);
    }
  },
};
