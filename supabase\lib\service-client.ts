import { Database } from "shared/lib/supabase/database";
import { createClient as createSupabaseClient } from "@supabase/supabase-js";
import { load } from "dotenv-mono";

// Load environment variables
load();

/**
 * Service client for administrative operations
 * Uses the service role key to bypass RLS policies
 */
export function createServiceClient() {
  return createSupabaseClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

// Export a singleton instance for convenience
export const serviceClient = createServiceClient();
