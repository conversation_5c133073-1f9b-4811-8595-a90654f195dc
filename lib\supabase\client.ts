import { createClient as createBrowserClient } from "@supabase/supabase-js";
import { Database } from "./database";

export function createClient() {
  // Support both dashboard (SUPABASE_URL) and webapp (NEXT_PUBLIC_SUPABASE_URL) environments
  const supabaseUrl =
    process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey =
    process.env.SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error("Missing Supabase environment variables");
  }

  return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey);
}
